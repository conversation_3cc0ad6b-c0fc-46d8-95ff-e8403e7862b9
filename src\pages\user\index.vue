<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '个人中心',
  },
}
</route>

<template>
  <view class="wrapper">
    <view class="top">
      <view class="flex items-center">
        <view class="avatar__inner">
          <image
            class="w-[130rpx] h-[130rpx] border-rd-[50%]"
            :src="userInfo.avatar || '/static/images/default-avatar.png'"
          ></image>
          <!-- <view class="icon">
            <text class="iconfont icon-xiangji"></text>
          </view> -->
        </view>
        <view class="flex-1">
          <view class="nickname">{{ userInfo.nickname || '资料未完成' }}</view>
        </view>
        <view class="edit">
          <wd-icon name="setting1" size="26"></wd-icon>
          <text>编辑资料</text>
        </view>
      </view>
    </view>
    <view class="px-2">
      <view class="ul">
        <view class="ul__title">我的服务</view>
        <view class="ul__group">
          <block v-for="(item, index) in links" :key="index">
            <navigator class="item" :url="item.path" open-type="navigate" hover-class="none">
              <view class="icon"><image :src="item.icon" mode="widthFix" class="icon-img" /></view>
              <view class="label">{{ item.label }}</view>
            </navigator>
          </block>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store/user'
import { storeToRefs } from 'pinia'
import { useMessage } from 'wot-design-uni'
//
const { userInfo, isComplete } = storeToRefs(useUserStore())
const { onShareAppMessage, onShareTimeline } = useShare()
const message = useMessage()
const self = reactive({
  system: null,
  height: 0,
})

const system = ref({})
uni.getSystemInfo({
  success: (res) => {
    const menu = uni.getMenuButtonBoundingClientRect()
    self.system = res
    self.height = (menu.top - self.system.statusBarHeight) * 2 + menu.height
  },
})
const onEdit = () => {
  uni.navigateTo({ url: '/pages/users/edit-profile' })
}
const toPage = (status) => {
  switch (status) {
    case 1:
      uni.navigateTo({ url: '/pages/users/profile' })
      break
    case 2:
      uni.navigateTo({ url: '/pages/login/realname' })
      break
    case 3:
      uni.navigateTo({ url: '/pages/users/education' })
      break
    case 4:
      uni.navigateTo({ url: '/pages/users/agreement?title=隐私协议&id=3' })
      break
    case 5:
      uni.navigateTo({ url: '/pages/users/agreement?title=用户协议&id=4' })
      break
    case 7:
      uni.navigateTo({ url: '/pages/message/index' })
      break
  }
}
onLoad(() => {
  // if (!isComplete.value) {
  //   message
  //     .confirm({
  //       msg: '您的资料不完整，暂时无法享受全部功能',
  //       title: '系统提醒',
  //       confirmButtonText: '立即完善',
  //       cancelButtonText: '稍后再说',
  //     })
  //     .then(() => {
  //       uni.navigateTo({ url: '/pages/login/information' })
  //     })
  //     .catch(() => {
  //       console.log('点击了取消按钮')
  //     })
  // }
})

const links = [
  {
    icon: '/static/images/user-menu-1.png',
    // label: '我的拼单',
    label: '我的活动',
    path: '/pages/users/my_activity/index',
  },
  {
    icon: '/static/images/user-menu-2.png',
    label: '我的发帖',
    path: '/pages/users/my_post/index',
  },
  {
    icon: '/static/images/user-menu-3.png',
    label: '我的跑团',
    path: '',
  },
  {
    icon: '/static/images/user-menu-4.png',
    label: '我的消息',
    path: '',
  },
]
</script>

<style lang="scss">
//
.wrapper {
  position: relative;
  min-height: 100vh;
  padding-bottom: 40rpx;
  background-color: #f3f2fd;
}

.top {
  width: 100%;
  padding: 80rpx 30rpx;
  background: #c5f355;
  .avatar__inner {
    position: relative;
    border-radius: 50%;
    .icon {
      position: absolute;
      top: 92rpx;
      left: 91rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36rpx;
      height: 36rpx;
      background: #333333;
      border-radius: 50%;

      .iconfont {
        font-size: 22rpx;
        color: #fff;
      }
    }
  }

  .nickname {
    font-weight: 400;
    font-size: 48rpx;
    color: #1a1a1a;
    padding-left: 20rpx;
  }
  .edit {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    font-weight: 400;
    font-size: 26rpx;
    color: #1a1a1a;
  }

  .info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 60rpx;
    margin-top: 50rpx;

    &__item {
      text-align: center;

      .label {
        font-size: 20rpx;
        color: #7f7f7f;
      }

      .num {
        margin-top: 24rpx;
        font-size: 32rpx;
        color: #303030;
      }
    }
  }
}

.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 114rpx;
  height: 40rpx;
  padding: 0;
  margin: 0;
  margin-left: auto;
  font-size: 22rpx;
  color: #2c285c;
  background: white;
  border-radius: 14rpx;
}

.ul {
  padding: 40rpx 30rpx;
  margin-top: 10rpx;
  background: #ffffff;
  border-radius: 16rpx;
  &__title {
    font-weight: 400;
    font-size: 32rpx;
    color: #333333;
    margin-bottom: 40rpx;
  }
  &__group {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 40rpx;
    .item {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
    }
    .icon-img {
      width: 50rpx;
      height: 56rpx;
    }
    .label {
      font-weight: 400;
      font-size: 24rpx;
      color: #1a1a1a;
      margin-top: 8rpx;
    }
  }
}
</style>
