<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '我的发帖',
  },
}
</route>

<template>
  <view class="">
    <!-- 状态筛选栏 -->
    <view class="activity-status-box">
      <view
        class="activity-status-item"
        :class="{ active: currentStatus === item.key }"
        v-for="item in statusList"
        :key="item.key"
        @click="handleStatusChange(item.key)"
      >
        <text class="label">
          {{ item.label }}
        </text>
      </view>
    </view>
    <!-- 热门话题板块 -->
    <view class="hot-section">
      <view class="topic-list">
        <view v-for="item in hotTopics" :key="item.id" class="topic-item">
          <view class="topic-image-wrapper">
            <image class="topic-image" :src="item.image_input?.[0]" mode="aspectFill"></image>
          </view>
          <view class="topic-info">
            <view class="topic-title">
              <view class="title">{{ item.title }}</view>
              <view class="status">已驳回</view>
            </view>
            <view class="topic-btns">
              <view class="action-buttons">
                <!-- 取消按钮 - 在审核中、拼单中状态显示 -->
                <button type="button" class="btn btn-delete">取消</button>

                <!-- 编辑按钮 - 在审核中状态显示 -->
                <button type="button" class="btn btn-edit">编辑</button>

                <!-- 已拼成按钮 - 在已拼成状态显示（不可点击） -->
                <!-- <button type="button" class="btn btn-edit">已拼成</button> -->

                <!-- 分享按钮 - 在已拼成、已结束状态显示 -->
                <button type="button" class="btn btn-share">分享</button>

                <!-- 删除按钮 - 在已驳回、已结束状态显示 -->
                <button type="button" class="btn btn-delete">删除</button>

                <!-- 重新报名按钮 - 在已驳回状态显示 -->
                <!-- <button type="button" class="btn btn-reapply">重新报名</button> -->
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
//

// 状态列表定义
const statusList = [
  { key: 'all', label: '全部' },
  { key: 'checking', label: '审核中' },
  { key: 'rejected', label: '已驳回/已取消' },
  { key: 'grouped', label: '已发布' },
]

const currentStatus = ref('all')

const hotTopics = [
  {
    id: 1,
    title: '请问特斯拉4S店贴的3M膜怎么样？',
    image_input: ['https://dummyimage.com/148x148/3c9cff/fff'],
    user: {
      nickname: '美丽人生',
      avatar: '/static/images/login-female-default.png',
    },
    add_time_date: '2小时前',
  },
  {
    id: 2,
    title: '贴车衣有必要吗？',
    image_input: ['https://dummyimage.com/148x148/3c9cff/fff'],
    user: {
      nickname: '美丽人生',
      avatar: '/static/images/login-male-default.png',
    },
    add_time_date: '5小时前',
  },
]
// 状态切换处理
const handleStatusChange = (status: string) => {
  currentStatus.value = status
  // 重置分页并重新加载数据
}
</script>

<style lang="scss" scoped>
//
.activity-status-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;

  .activity-status-item {
    font-size: 26rpx;
    color: #303030;
    transition: all 0.3s;
    position: relative;
    .label {
      position: relative;
      z-index: 99;
    }
  }

  .active {
    color: #000000;
    font-weight: bold;
    &::after {
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 16rpx;
      background: #c5f355;
      border-radius: 20rpx;
      z-index: 2;
    }
  }
}
// 话题列表样式
.topic-list {
  .topic-item {
    display: flex;
    padding: 20rpx;
    margin-bottom: 20rpx;
    background: #fff;

    .topic-image-wrapper {
      margin-right: 20rpx;

      .topic-image {
        width: 148rpx;
        height: 148rpx;
        border-radius: 20rpx;
      }
    }

    .topic-info {
      flex: 1;

      .topic-title {
        margin-bottom: 40rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .title {
          font-weight: bold;
          font-size: 28rpx;
          color: #333333;
          line-height: 1.4;
          -webkit-box-orient: vertical; /* 垂直排列 */
          -webkit-line-clamp: 3; /* 限制显示三行 */
          overflow: hidden; /* 隐藏超出范围的内容 */
          text-overflow: ellipsis; /* 使用省略号 */
        }
        .status {
          font-weight: bold;
          font-size: 22rpx;
          color: #1a1a1a;
          margin-left: 20rpx;
        }
      }
      .action-buttons {
        display: flex;
        gap: 16rpx;
        justify-content: flex-end;
        .btn {
          border-radius: 36rpx;
          font-size: 26rpx;
          border: none;
          margin: 0;
          transition: all 0.3s;
          line-height: normal;
          font-weight: 500;
          font-size: 26rpx;
          color: #000000;
          padding: 6rpx 20rpx;
          &.btn-cancel {
            background-color: #f8f8f8;
            color: #666;
            border: 1rpx solid #ddd;
          }

          &.btn-edit {
            background-color: #f8f8f8;
            color: #000000;
            border: 1rpx solid #000000;
          }

          &.btn-grouped {
            background-color: #e8f5e8;
            color: #52c41a;
            border: 1rpx solid #b7eb8f;
          }

          &.btn-share {
            background: #c5f355;
            border-radius: 24rpx;
          }

          &.btn-delete {
            font-weight: 400;
            font-size: 26rpx;
            color: #666666;
            background-color: transparent;
          }

          &.btn-reapply {
            background: linear-gradient(24deg, #ff94b2, #f9225e);
            color: #fff;
            border: none;
          }
        }
      }
    }
  }
}
</style>
