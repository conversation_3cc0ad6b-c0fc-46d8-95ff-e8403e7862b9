<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '我的活动',
  },
}
</route>

<template>
  <view class="my-activity-container">
    <!-- 状态筛选栏 -->
    <view class="activity-status-box">
      <view
        class="activity-status-item"
        :class="{ active: currentStatus === item.key }"
        v-for="item in statusList"
        :key="item.key"
        @click="handleStatusChange(item.key)"
      >
        <text class="label">
          {{ item.label }}
        </text>
      </view>
    </view>

    <view class="companion-card" v-for="item in displayedCompanionList" :key="item.id">
      <view class="card-header">
        <view class="user-info">
          <image
            class="user-avatar"
            :src="item.user?.avatar || '/static/images/login-female-default.png'"
            mode="aspectFill"
          ></image>
          <view class="user-details">
            <text class="user-name">{{ item.user?.nickname || '美丽人生' }}</text>
            <!-- <view class="user-location">
              <image src="/static/images/icon-qizhi.png" class="icon" mode="scaleToFill" />
              <text class="location-text">{{ item.location || '天府跑团' }}</text>
            </view> -->
          </view>
        </view>
        <!-- <view class="post-btn-small" @click.stop="toPost">
          <wd-icon name="add" size="12" color="#333"></wd-icon>
          <text class="post-text">发布</text>
        </view> -->
        <view class="status">已拼成</view>
      </view>

      <view class="card-content">
        <view class="content-title">{{ item.title }}</view>
        <view class="flex items-center">
          <image
            src="https://dummyimage.com/148x148/3c9cff/fff"
            mode="scaleToFill"
            class="avatar"
          />
          <view class="">
            <view class="content-tags">
              <view class="tag price-tag">{{ item.price }}</view>
              <view class="tag type-tag">{{ item.type }}</view>
              <view class="tag gender-tag">{{ item.gender }}</view>
            </view>
            <view class="content-info">
              <view class="info-item">
                <image src="/static/images/icon-saishi.png" class="icon" mode="scaleToFill" />
                <text class="info-text">{{ item.event_name }}</text>
              </view>
              <view class="info-item">
                <image src="/static/images/icon-time-2.png" class="icon" mode="scaleToFill" />
                <text class="info-text">{{ item.date_time }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="footer">
        <view class="action-buttons">
          <!-- 取消按钮 - 在审核中、拼单中状态显示 -->
          <button
            v-if="showCancelButton(item.status)"
            type="button"
            class="btn btn-delete"
            @click.stop="handleCancel(item)"
          >
            取消
          </button>

          <!-- 编辑按钮 - 在审核中状态显示 -->
          <button
            v-if="showEditButton(item.status)"
            type="button"
            class="btn btn-edit"
            @click.stop="handleEdit(item)"
          >
            编辑
          </button>

          <!-- 已拼成按钮 - 在已拼成状态显示（不可点击） -->
          <button v-if="item.status === 'grouped'" type="button" class="btn btn-edit">
            已拼成
          </button>

          <!-- 分享按钮 - 在已拼成、已结束状态显示 -->
          <button
            v-if="showShareButton(item.status)"
            type="button"
            class="btn btn-share"
            @click.stop="handleShare(item)"
          >
            分享
          </button>

          <!-- 删除按钮 - 在已驳回、已结束状态显示 -->
          <button
            v-if="showDeleteButton(item.status)"
            type="button"
            class="btn btn-delete"
            @click.stop="handleDelete(item)"
          >
            删除
          </button>

          <!-- 重新报名按钮 - 在已驳回状态显示 -->
          <button
            v-if="item.status === 'rejected'"
            type="button"
            class="btn btn-reapply"
            @click.stop="handleReapply(item)"
          >
            重新报名
          </button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-if="filteredActivityList.length === 0">
      <image class="empty-image" src="/static/images/icon-message.png" mode="aspectFit" />
      <text class="empty-text">暂无相关活动</text>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" v-if="!pageData.loadend && filteredActivityList.length > 0">
      <text>{{ pageData.loading ? '加载中...' : '上拉加载更多' }}</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { myActivityList, myCancelJoin, myDelJoin } from '@/service/user/index'
import { useToast } from 'wot-design-uni'

const toast = useToast()

// 状态列表定义
const statusList = [
  { key: 'all', label: '全部' },
  { key: 'checking', label: '审核中' },
  { key: 'rejected', label: '已驳回' },
  { key: 'grouping', label: '拼单中' },
  { key: 'grouped', label: '已拼成' },
  { key: 'finished', label: '已结束' },
]

// 响应式数据
const currentStatus = ref('all')
const activityList = ref<any[]>([])
const pageData = reactive({
  page: 1,
  limit: 10,
  loading: false,
  loadend: false,
})

// 计算属性：根据状态筛选活动列表
const filteredActivityList = []

// 状态切换处理
const handleStatusChange = (status: string) => {
  currentStatus.value = status
  // 重置分页并重新加载数据
  resetAndLoad()
}

// 重置分页并重新加载
const resetAndLoad = () => {
  pageData.page = 1
  pageData.loadend = false
  activityList.value = []
  loadActivityList()
}

// 加载活动列表
const loadActivityList = async () => {
  if (pageData.loading || pageData.loadend) return

  pageData.loading = true

  const params = {
    page: pageData.page,
    limit: pageData.limit,
    status: currentStatus.value === 'all' ? '' : currentStatus.value,
  }

  const [res, err] = await myActivityList(params)

  if (res) {
    const list = res.data.list || []
    const loadend = list.length < pageData.limit
    pageData.loadend = loadend
    pageData.page++

    if (pageData.page === 2) {
      activityList.value = list
    } else {
      activityList.value = [...activityList.value, ...list]
    }
  } else if (err) {
    toast.error(err.message || '加载失败')
  }

  pageData.loading = false
}

// 按钮显示逻辑
const showCancelButton = (status: string) => {
  // return ['checking', 'grouping'].includes(status)
  return true
}

const showEditButton = (status: string) => {
  //   return status === 'checking'
  return true
}

const showShareButton = (status: string) => {
  //   return ['grouped', 'finished'].includes(status)
  return true
}

const showDeleteButton = (status: string) => {
  //   return ['rejected', 'finished'].includes(status)
  return true
}

// 按钮事件处理
const handleCancel = async (item: any) => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消这个活动吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          // 调用取消接口
          const [result, error] = await myCancelJoin(item.id)
          if (result) {
            toast.success('取消成功')
            resetAndLoad()
          } else {
            toast.error(error?.message || '取消失败')
          }
        } catch (err) {
          toast.error('取消失败')
        }
      }
    },
  })
}

const handleEdit = (item: any) => {
  // 跳转到编辑页面
  uni.navigateTo({
    url: `/pages/activity/edit?id=${item.id}`,
  })
}

const handleShare = (item: any) => {
  // 分享功能
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    href: `pages/activity/activity-detail?id=${item.id}`,
    title: item.title,
    summary: `我参加了${item.title}，快来看看吧！`,
    imageUrl: item.image_input?.[0] || '/static/images/banner.png',
    success: () => {
      toast.success('分享成功')
    },
    fail: () => {
      toast.error('分享失败')
    },
  })
}

const handleDelete = async (item: any) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这个活动记录吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          // 调用删除接口
          const [result, error] = await myDelJoin(item.id)
          if (result) {
            toast.success('删除成功')
            resetAndLoad()
          } else {
            toast.error(error?.message || '删除失败')
          }
        } catch (err) {
          toast.error('删除失败')
        }
      }
    },
  })
}

const handleReapply = (item: any) => {
  // 跳转到重新报名页面
  uni.navigateTo({
    url: `/pages/activity/activity-detail?id=${item.id}`,
  })
}

const displayedCompanionList = [
  {
    id: 1,
    title: '预算100-150找10月25日成马起点附近拼房',
    price: '60',
    type: '拼房',
    typeKey: 'house',
    gender: '限男',
    event_name: '2025大邑安仁半程马拉松',
    date_time: '25/07/19 12:00:00—25/07/19 12:00:00',
    location: '天府跑团',
    user: {
      nickname: '美丽人生',
      avatar: '/static/images/login-female-default.png',
    },
  },
  {
    id: 2,
    title: '9月15日去比赛，成马赛道沿线求拼车',
    price: 'AA',
    type: '拼车',
    typeKey: 'car',
    gender: '不限',
    event_name: '2025重庆(长嘉汇)半程马拉松',
    date_time: '25/09/15 06:30—25/09/15 12:00',
    location: '南门—比赛起点',
    user: {
      nickname: '跑在成都',
      avatar: '/static/images/login-male-default.png',
    },
  },
  {
    id: 3,
    title: '周末早上天府绿道10公里配速5:30，求陪跑',
    price: '免费',
    type: '私兔',
    typeKey: 'pace',
    gender: '不限',
    event_name: '天府绿道训练',
    date_time: '每周六 7:00—8:30',
    location: '世纪城地铁站集合',
    user: {
      nickname: '小兔快快',
      avatar: '/static/images/login-female-default.png',
    },
  },
]

// 页面加载时获取数据
onLoad(() => {
  loadActivityList()
})

// 下拉刷新
onPullDownRefresh(() => {
  resetAndLoad()
  uni.stopPullDownRefresh()
})

// 上拉加载更多
onReachBottom(() => {
  loadActivityList()
})
</script>

<style lang="scss" scoped>
.my-activity-container {
  min-height: 100vh;
  //   background-color: #f5f5f5;
}

.activity-status-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;

  .activity-status-item {
    font-size: 26rpx;
    color: #303030;
    transition: all 0.3s;
    position: relative;
    .label {
      position: relative;
      z-index: 99;
    }
  }

  .active {
    color: #000000;
    font-weight: bold;
    &::after {
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 16rpx;
      background: #c5f355;
      border-radius: 20rpx;
      z-index: 2;
    }
  }
}
.companion-card {
  background: #fff;
  padding: 24rpx;
  margin-bottom: 10rpx;

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx;

    .user-info {
      display: flex;
      align-items: center;

      .user-avatar {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        margin-right: 16rpx;
      }

      .user-details {
        display: flex;
        align-items: center;
        .user-name {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
          margin-right: 16rpx;
        }

        .user-location {
          display: flex;
          align-items: center;
          .icon {
            width: 24rpx;
            height: 26rpx;
          }

          .location-text {
            font-size: 24rpx;
            color: #999;
            margin-left: 8rpx;
          }
        }
      }
    }

    .post-btn-small {
      display: flex;
      align-items: center;
      padding: 8rpx 16rpx;
      background: #c5f355;
      border-radius: 20rpx;

      .post-text {
        font-size: 24rpx;
        color: #333;
        margin-left: 8rpx;
      }
    }
    .status {
      font-weight: 500;
      font-size: 26rpx;
      color: #1a1a1a;
    }
  }

  .card-content {
    .content-title {
      font-weight: 400;
      font-size: 26rpx;
      color: #333333;
      margin-bottom: 16rpx;
    }
    .avatar {
      width: 148rpx;
      height: 148rpx;
      border-radius: 16rpx;
      margin-right: 12rpx;
    }

    .content-tags {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;

      .tag {
        padding: 6rpx 12rpx;
        border-radius: 16rpx;
        font-size: 22rpx;
        margin-right: 12rpx;

        &.price-tag {
          font-weight: 400;
          font-size: 28rpx;
          color: #ed1212;
          &::before {
            content: '￥';
            font-size: 22rpx;
          }
        }

        &.type-tag {
          background: #c5f355;
          border-radius: 6rpx;
        }

        &.gender-tag {
          border-radius: 6rpx;
          border: 1px solid #1a1a1a;
          font-weight: 400;
          font-size: 22rpx;
          color: #1a1a1a;
        }
      }
    }

    .content-info {
      .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 8rpx;
        .icon {
          width: 28rpx;
          height: 28rpx;
        }

        .info-text {
          font-size: 24rpx;
          color: #666;
          margin-left: 8rpx;
        }
      }
    }
  }
}

.activity-list {
  padding: 20rpx;
}

.activity-item {
  margin-bottom: 20rpx;
}

.activity-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  display: flex;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.activity-image-wrapper {
  position: relative;
  margin-right: 20rpx;

  .activity-image {
    width: 200rpx;
    height: 200rpx;
    border-radius: 12rpx;
  }

  .activity-status-tag {
    position: absolute;
    top: 8rpx;
    left: 8rpx;
    padding: 4rpx 12rpx;
    border-radius: 12rpx;
    font-size: 20rpx;
    color: #fff;

    &.status-checking {
      background-color: #ff9500;
    }

    &.status-rejected {
      background-color: #ff3b30;
    }

    &.status-grouping {
      background-color: #007aff;
    }

    &.status-grouped {
      background-color: #34c759;
    }

    &.status-finished {
      background-color: #8e8e93;
    }

    &.status-default {
      background-color: #666;
    }
  }
}

.activity-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.activity-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 16rpx;
}

.activity-meta {
  .activity-time,
  .activity-location {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: #666;
    margin-bottom: 8rpx;

    .iconfont {
      margin-right: 8rpx;
      font-size: 24rpx;
    }
  }
}

.activity-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #f9225e;
  margin-top: 16rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .empty-image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 20rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

.load-more {
  padding: 20rpx;
  text-align: center;
  font-size: 24rpx;
  color: #999;
}

// 按钮样式
.footer {
  .action-buttons {
    display: flex;
    gap: 16rpx;
    justify-content: flex-end;
    .btn {
      border-radius: 36rpx;
      font-size: 26rpx;
      border: none;
      margin: 0;
      transition: all 0.3s;
      line-height: normal;
      font-weight: 500;
      font-size: 26rpx;
      color: #000000;
      padding: 6rpx 20rpx;
      &.btn-cancel {
        background-color: #f8f8f8;
        color: #666;
        border: 1rpx solid #ddd;
      }

      &.btn-edit {
        background-color: #f8f8f8;
        color: #000000;
        border: 1rpx solid #000000;
      }

      &.btn-grouped {
        background-color: #e8f5e8;
        color: #52c41a;
        border: 1rpx solid #b7eb8f;
      }

      &.btn-share {
        background: #c5f355;
        border-radius: 24rpx;
      }

      &.btn-delete {
        font-weight: 400;
        font-size: 26rpx;
        color: #666666;
        background-color: transparent;
      }

      &.btn-reapply {
        background: linear-gradient(24deg, #ff94b2, #f9225e);
        color: #fff;
        border: none;
      }
    }
  }
}
</style>
